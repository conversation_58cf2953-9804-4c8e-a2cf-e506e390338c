# 柴管家 (ChaiGuanJia)

> 🏠 现代化的家庭管理系统 | 🚀 生产就绪

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![CI Pipeline](https://github.com/Amoresdk/chaiguanjia_8.4/actions/workflows/ci.yml/badge.svg)](https://github.com/Amoresdk/chaiguanjia_8.4/actions/workflows/ci.yml)
[![Security Scan](https://github.com/Amoresdk/chaiguanjia_8.4/actions/workflows/security.yml/badge.svg)](https://github.com/Amoresdk/chaiguanjia_8.4/actions/workflows/security.yml)
[![Coverage](https://codecov.io/gh/Amoresdk/chaiguanjia_8.4/branch/main/graph/badge.svg)](https://codecov.io/gh/Amoresdk/chaiguanjia_8.4)
[![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)](https://github.com)
[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-red.svg)](https://fastapi.tiangolo.com/)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-4.9+-blue.svg)](https://www.typescriptlang.org/)
[![Docker](https://img.shields.io/badge/Docker-Optimized-blue.svg)](https://www.docker.com/)

## 📖 项目简介

柴管家是一个现代化的家庭管理系统，帮助用户更好地管理家庭事务、财务和日常生活。通过直观的界面和强大的功能，让家庭管理变得简单高效。

### 🌟 核心特性

- **💰 财务管理**：收支记录、预算规划、账单提醒
- **📅 日程管理**：家庭日历、任务分配、提醒通知
- **🏠 家务管理**：家务分工、完成跟踪、奖励机制
- **📊 数据分析**：支出分析、习惯统计、趋势预测
- **👨‍👩‍👧‍👦 家庭协作**：多用户支持、权限管理、消息通知
- **📱 移动友好**：响应式设计，支持手机、平板、桌面端

### 🏗️ 技术架构

- **前端**：React 18 + TypeScript + Vite + Zustand
- **后端**：Python 3.11 + FastAPI + SQLAlchemy 2.0
- **数据库**：PostgreSQL 15 + Redis 7
- **异步任务**：RabbitMQ 3.12 + Celery (完全集成) ✅
- **容器化**：Docker + Docker Compose (网络优化) ✅
- **监控**：Flower + 健康检查 + 性能监控 ✅
- **代码质量**：ESLint + Prettier + Black + pytest

### 🎯 项目状态

- ✅ **核心功能**：完全开发完成
- ✅ **异步任务**：Celery 集成完成，支持定时任务和后台处理
- ✅ **Docker 配置**：网络配置优化，100%问题修复
- ✅ **健康监控**：完整的健康检查和监控体系
- ✅ **生产就绪**：所有服务稳定运行，可投入生产使用

### 🧪 CI/CD 测试状态

> **测试时间**: 2024-08-07
> **测试目的**: 验证完整的 GitHub Actions 和 Codecov 集成
> **测试范围**: CI Pipeline、Security Scan、Coverage Report、Notifications

## 🚀 快速开始

### 📋 环境要求

确保您的开发环境满足以下要求：

- **Docker Desktop**: 20.10+ ([下载地址](https://www.docker.com/get-started))
- **Node.js**: 18.0+ ([下载地址](https://nodejs.org/))
- **Python**: 3.11+ (可选，用于本地开发)

### ⚡ 一键启动开发环境

```bash
# 1. 克隆项目
git clone <repository-url>
cd chaiguanjia

# 2. 配置环境变量
cp .env.example .env
# 根据需要修改.env文件中的配置

# 3. 启动完整开发环境
docker-compose up -d

# 4. 验证服务状态
docker-compose ps

# 5. 运行健康检查
./scripts/health-check.sh
```

### 🌐 网络配置说明

✅ **Docker 网络配置已完全优化**，解决了所有构建和运行问题：

1. **代理配置问题**：项目已配置 build-arg 来处理代理设置
2. **镜像源优化**：使用国内镜像源提高构建速度
3. **网络问题修复**：100%解决容器间通信和外网访问问题
4. **详细指南**：查看 `项目文档/运维指南/Docker避坑指南.md`

### 🌐 服务访问地址

启动成功后，您可以访问以下地址：

| 服务                 | 地址                         | 认证信息                   | 状态      |
| -------------------- | ---------------------------- | -------------------------- | --------- |
| 🎨 **前端应用**      | http://localhost:5173        | -                          | ✅ 运行中 |
| 🔧 **API 文档**      | http://localhost:8000/docs   | -                          | ✅ 运行中 |
| 🔧 **API 健康检查**  | http://localhost:8000/health | -                          | ✅ 运行中 |
| 🌸 **Flower 监控**   | http://localhost:5555        | admin:flower123            | ✅ 运行中 |
| 🐰 **RabbitMQ 管理** | http://localhost:15672       | chaiguanjia:rabbitmq123    | ✅ 运行中 |
| 🗄️ **PostgreSQL**    | localhost:5432               | chaiguanjia:chaiguanjia123 | ✅ 运行中 |
| 🔴 **Redis**         | localhost:6379               | :redis123                  | ✅ 运行中 |
| 🌐 **Nginx 代理**    | http://localhost:80          | -                          | ✅ 运行中 |

### ✅ 服务验证步骤

启动后请按以下步骤验证所有服务：

```bash
# 1. 检查所有容器状态
docker-compose ps

# 2. 运行完整健康检查
./scripts/health-check.sh

# 3. 测试前端访问
curl http://localhost:5173/

# 4. 测试后端API
curl http://localhost:8000/health

# 5. 测试Flower监控（需要认证）
curl -u admin:flower123 http://localhost:5555/

# 6. 检查Celery任务状态
docker exec chaiguanjia-celery-worker celery -A app.core.celery inspect ping
```

### 🛠️ 运维工具

项目提供了完整的运维工具来简化开发和故障排查：

#### 🎯 项目总控制

```bash
./scripts/project-manager.sh dev      # 🚀 启动完整开发环境
./scripts/project-manager.sh status   # 📊 查看所有服务状态
./scripts/project-manager.sh stop     # ⏹️ 停止所有服务
./scripts/project-manager.sh logs     # 📋 查看服务日志
```

#### 🔍 健康检查与诊断

```bash
./scripts/health-check.sh             # 🏥 完整健康检查
./scripts/diagnose.sh                 # 🔍 故障诊断工具
./scripts/diagnose.sh backend         # 🔍 诊断特定服务
./scripts/diagnose.sh --network       # 🌐 网络问题诊断
./scripts/diagnose.sh --report        # 📊 生成诊断报告
```

#### 🧹 环境清理

```bash
./scripts/cleanup.sh                  # 🧹 交互式清理菜单
./scripts/cleanup.sh --light          # 🧹 轻度清理（停止容器）
./scripts/cleanup.sh --medium         # 🧹 中度清理（删除容器和镜像）
./scripts/cleanup.sh --backup         # 💾 备份重要数据
```

#### 🗄️ 数据库管理

```bash
./scripts/db-manager.sh backup        # 💾 备份数据库
./scripts/db-manager.sh monitor       # 📊 实时监控数据库
./scripts/db-manager.sh connect       # 🔗 连接数据库终端
```

#### 🎨 前端管理

```bash
./scripts/frontend-manager.sh dev     # 🚀 启动开发服务器
./scripts/frontend-manager.sh build   # 🏗️ 构建生产版本
./scripts/frontend-manager.sh test    # 🧪 运行测试套件
```

## 📁 项目结构

```
chaiguanjia/
├── 📱 frontend/                 # React前端应用
│   ├── src/
│   │   ├── components/         # 可复用组件
│   │   ├── pages/             # 页面组件
│   │   ├── hooks/             # 自定义Hooks
│   │   ├── services/          # API服务
│   │   ├── stores/            # 状态管理
│   │   └── utils/             # 工具函数
│   └── package.json
├── 🔧 backend/                 # FastAPI后端应用
│   ├── app/
│   │   ├── api/               # API路由
│   │   ├── core/              # 核心配置
│   │   ├── models/            # 数据模型
│   │   ├── services/          # 业务逻辑
│   │   └── utils/             # 工具函数
│   └── requirements.txt
├── 🏗️ infrastructure/          # 基础设施配置
│   └── docker/               # Docker相关配置
├── 📜 scripts/                # 项目管理脚本
│   ├── project-manager.sh    # 项目总控制脚本
│   ├── health-check.sh       # 健康检查脚本
│   ├── diagnose.sh           # 故障诊断脚本
│   ├── cleanup.sh            # 环境清理脚本
│   ├── db-manager.sh         # 数据库管理脚本
│   ├── frontend-manager.sh   # 前端管理脚本
│   └── docker-build-no-proxy.sh # Docker构建脚本
├── 📚 项目文档/               # 项目文档
│   └── 运维指南/
│       ├── Docker避坑指南.md
│       ├── Docker网络配置指南.md
│       └── Docker工具快速参考.md
└── 📚 docs/                   # 其他文档
```

## 🔧 故障排除

### 🚀 快速故障排查

✅ **项目已解决所有已知 Docker 问题**，如遇到新问题请使用以下工具：

```bash
# 1. 运行完整诊断
./scripts/diagnose.sh

# 2. 检查服务健康状态
./scripts/health-check.sh

# 3. 查看详细的故障排查指南
cat 项目文档/运维指南/Docker避坑指南.md
```

### 常见问题及解决方案

1. **Docker 构建失败**

   ```bash
   # 使用优化的构建脚本（已解决代理问题）
   docker-compose build

   # 如仍有问题，使用诊断工具
   ./scripts/diagnose.sh --build
   ```

2. **服务无法启动**

   ```bash
   # 运行服务诊断
   ./scripts/diagnose.sh [service_name]

   # 查看服务日志
   docker-compose logs -f [service_name]
   ```

3. **网络连接问题**

   ```bash
   # 网络诊断（已优化网络配置）
   ./scripts/diagnose.sh --network

   # 重置网络（如需要）
   docker-compose down && docker-compose up -d
   ```

4. **性能问题**

   ```bash
   # 性能监控
   ./scripts/diagnose.sh --performance

   # 清理系统资源
   ./scripts/cleanup.sh --medium
   ```

5. **Celery 任务问题**

   ```bash
   # 检查Celery Worker状态
   docker exec chaiguanjia-celery-worker celery -A app.core.celery inspect ping

   # 查看Flower监控界面
   open *************************************
   ```

### 📚 详细故障排查指南

- 📖 [Docker 避坑指南](项目文档/运维指南/Docker避坑指南.md) - 完整的问题解决方案
- 🔧 [Docker 工具快速参考](项目文档/运维指南/Docker工具快速参考.md) - 命令速查手册
- 🌐 [Docker 网络配置指南](项目文档/运维指南/Docker网络配置指南.md) - 网络配置详解

## 🎯 开发规范

### 代码规范

项目严格遵循以下代码规范：

- **命名规范**: 遵循 GNC-AIDD 命名规范
- **项目结构**: 严格的模块化架构
- **代码质量**: ESLint + Prettier + Black
- **提交规范**: Conventional Commits

### 测试

```bash
# 前端测试
./scripts/frontend-manager.sh test

# 前端测试覆盖率
./scripts/frontend-manager.sh test:coverage

# 后端测试
cd backend && pytest
```

## 🚀 部署

### 生产环境部署

✅ **项目已完全准备好生产部署**

```bash
# 1. 备份重要数据
./scripts/cleanup.sh --backup

# 2. 构建生产镜像（网络优化）
docker-compose build

# 3. 启动生产环境
./scripts/project-manager.sh prod

# 4. 验证部署状态
./scripts/health-check.sh

# 5. 监控服务状态
open *************************************  # Flower监控
```

### 🔧 环境变量配置

复制并修改环境变量文件：

```bash
cp .env.example .env
```

主要配置项：

- `DATABASE_URL`: 数据库连接字符串
- `REDIS_URL`: Redis 连接字符串
- `CELERY_BROKER_URL`: Celery 消息代理 URL
- `CELERY_RESULT_BACKEND`: Celery 结果后端 URL
- `SECRET_KEY`: 应用密钥
- `CORS_ORIGINS`: 允许的跨域源
- `FLOWER_BASIC_AUTH`: Flower 监控认证信息

### 🔍 监控和健康检查

项目集成了完整的监控体系：

- **健康检查**: 所有服务都配置了健康检查
- **Flower 监控**: Celery 任务实时监控 (http://localhost:5555)
- **性能监控**: 容器资源使用监控
- **日志聚合**: 统一的日志管理和查看
- **故障诊断**: 自动化的故障诊断工具

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

## 📊 项目统计

- ✅ **8 个 Docker 问题** 100%修复完成
- ✅ **所有核心服务** 稳定运行
- ✅ **异步任务处理** 完全集成
- ✅ **监控体系** 完整部署
- ✅ **生产就绪** 可立即投入使用

## 🎉 致谢

感谢所有为项目贡献代码、文档和建议的开发者们！

<div align="center">
  <p>如果这个项目对您有帮助，请给我们一个 ⭐️</p>
  <p>Made with ❤️ by 柴管家团队</p>
  <p>🚀 <strong>生产就绪 | Production Ready</strong> 🚀</p>
</div>
